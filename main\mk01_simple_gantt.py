# -*- coding: utf-8 -*-
"""
MK01实例甘特图绘制脚本

功能：
1. 运行MK01实例的四种调度规则
2. 生成甘特图对比可视化
3. 输出详细的性能分析报告

技术特点：
- 使用matplotlib非交互后端，避免GUI依赖
- 支持中文字体显示
- 自动保存高分辨率图片
- 提供完整的性能指标分析

作者：AI Assistant
日期：2025年
"""

import matplotlib
matplotlib.use('Agg')  # 使用非交互后端，避免GUI依赖问题
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from test import FJSPScheduler

# 设置中文字体支持，解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

def create_mk01_gantt():
    """
    创建MK01实例四种调度规则的甘特图对比

    流程：
    1. 运行四种调度算法获取结果
    2. 配置颜色方案（每个工件一种颜色）
    3. 创建2x2子图布局
    4. 为每种规则绘制甘特图
    5. 添加图例和标注
    6. 保存高分辨率图片
    7. 输出性能分析报告

    返回:
        dict: 各调度规则的makespan结果
    """
    print("正在生成MK01甘特图...")

    # 步骤1：运行调度算法
    scheduler = FJSPScheduler()
    results, detailed_results = scheduler.run_all_rules()

    # 步骤2：颜色配置（10种颜色对应10个工件）
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
              '#DDA0DD', '#FFB6C1', '#98FB98', '#F0E68C', '#87CEEB']
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('MK01实例四种调度规则甘特图对比', fontsize=18, fontweight='bold')
    
    rules = ['FIFO', 'MOPNR', 'SPT', 'MWKR']
    positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
    
    for idx, (rule, (row, col)) in enumerate(zip(rules, positions)):
        if rule not in detailed_results:
            continue
            
        ax = axes[row, col]
        schedule = detailed_results[rule]['schedule']
        makespan = detailed_results[rule]['makespan']
        
        print(f"绘制{rule}甘特图，makespan={makespan}")
        
        # 绘制甘特图
        for item in schedule:
            job_id = item['job'] - 1  # 转换为0-based索引
            machine_id = item['machine'] - 1  # 转换为0-based索引
            start_time = item['start_time']
            processing_time = item['processing_time']
            operation_id = item['operation']
            
            # 创建矩形块
            rect = patches.Rectangle(
                (start_time, machine_id), processing_time, 0.8,
                linewidth=1, edgecolor='black',
                facecolor=colors[job_id % len(colors)], alpha=0.8
            )
            ax.add_patch(rect)
            
            # 添加标签（只在矩形足够大时显示）
            if processing_time > makespan * 0.03:  # 只在矩形宽度足够时显示文字
                ax.text(start_time + processing_time/2, machine_id + 0.4, 
                       f'J{job_id+1}',  # 简化标签
                       ha='center', va='center', fontsize=8, fontweight='bold')
        
        # 设置坐标轴
        ax.set_xlim(0, makespan + 2)
        ax.set_ylim(-0.5, 6 - 0.5)  # MK01有6台机器
        ax.set_xlabel('时间', fontsize=12)
        ax.set_ylabel('机器', fontsize=12)
        ax.set_title(f'{rule} (Makespan: {makespan})', fontsize=14, fontweight='bold')
        ax.set_yticks(range(6))
        ax.set_yticklabels([f'M{i+1}' for i in range(6)])
        ax.grid(True, alpha=0.3)
        
        # 添加makespan线
        ax.axvline(x=makespan, color='red', linestyle='--', linewidth=2, alpha=0.7)
        ax.text(makespan + 0.5, 3, f'Makespan\n{makespan}', 
               rotation=90, va='center', color='red', fontweight='bold', fontsize=10)
    
    # 添加图例
    num_jobs = 10  # MK01有10个工件
    legend_elements = [patches.Patch(facecolor=colors[i % len(colors)], 
                                   label=f'工件{i+1}') for i in range(num_jobs)]
    fig.legend(handles=legend_elements, loc='lower center', bbox_to_anchor=(0.5, 0.02), 
              ncol=5, fontsize=11)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93, bottom=0.12)
    
    # 保存图片
    filename = 'mk01_gantt_comparison.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"甘特图已保存为: {filename}")
    
    # 打印结果汇总
    print("\n=== MK01调度结果汇总 ===")
    print("规则\t\t总完工时间\t与最优解差距")
    print("-" * 50)
    
    best_makespan = min(results.values())
    for rule, makespan in results.items():
        gap = makespan - 42  # 与已知最优解上限的差距
        gap_str = f"+{gap}" if gap > 0 else f"{gap}" if gap < 0 else "0"
        print(f"{rule}\t\t{makespan}\t\t{gap_str}")
    
    print(f"\n最佳规则: {min(results.items(), key=lambda x: x[1])[0]}")
    print("MK01已知最优解范围: 36-42")
    
    if best_makespan <= 42:
        print("✅ 本次最佳结果在已知最优解范围内")
    else:
        print(f"⚠️ 本次最佳结果超出已知最优解范围 +{best_makespan - 42}")
    
    return results

if __name__ == "__main__":
    results = create_mk01_gantt()
    print("\n甘特图生成完成！")
